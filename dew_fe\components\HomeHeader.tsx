import React, { useEffect, useState, memo } from "react";
import { Pressable, Text, View } from "react-native";
import Colors from "../constants/Colors";
import { MotivationStyle } from "./MotivationStyleSelector";
import * as SecureStore from 'expo-secure-store';
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/hooks/useAuth";

type PledgeCurrentStreak = {
  user_id: string;
  current_streak: number;
  current_streak_started_on: string | null;
  last_pledged_on: string | null;
};

interface HomeHeaderProps {
  motivationStyle: MotivationStyle;
  onToggleToughLove: () => void;
  onStyleSelectorPress: () => void;
}

const HomeHeader = (function HomeHeader({
  motivationStyle,
  onToggleToughLove,
  onStyleSelectorPress,
}: HomeHeaderProps) {
  const { userId } = useAuth();
  const [streak, setStreak] = useState(0);
  const [userName, setUserName] = useState<string | null>(null);

const getTotalStreak = async () => {
  try {
    const { data, error } = await supabase
    .from('pledge_current_streaks')
    .select('*')
    .eq('user_id', userId)
    .maybeSingle();
  console.log("dataSTREAK", data);
  if (error) throw error;
  return data ?? null;
  } catch (error) {
    console.error("Error fetching streak:", error);
    return;
  }

};

  // Log only when userId changes from null to a value
  useEffect(() => {
    if (userId) {
      getTotalStreak();
    }
  }, [userId]);

  // Fetch user name
  useEffect(() => {
    const getUserName = async () => {
      const name = await SecureStore.getItemAsync("user_name");
      setUserName(name);
    };
    getUserName();
  }, []);

  // Fetch streak when userId is available
  useEffect(() => {
    const getStreak = async () => {
      if (!userId) return;

      const { data, error } = await supabase
        .from("pledges")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (error) {
        console.error("Error fetching streak:", error);
        return;
      }

      if (data) {
        setStreak(data.streak);
      }
    };

    getStreak();
  }, [userId]);

  return (
    <View className="flex-row justify-between items-center mb-8">
      <View>
        <Text
          style={{ color: Colors.text.secondary }}
          className="text-base"
        >
          Hi {userName || "there"},
        </Text>
        <View className="flex-row items-end">
          <Text
            style={{ color: Colors.text.primary }}
            className="text-4xl font-bold"
          >
            {streak || 0}
          </Text>
          <Text
            style={{ color: Colors.text.secondary }}
            className="text-2xl font-bold ml-1 mb-1"
          >
            {" "}
            Days
          </Text>
        </View>
        <Text
          style={{ color: Colors.brand.primary }}
          className="font-medium"
        >
          Weed-free journey
        </Text>
      </View>
      <View className="flex-row items-center gap-3">
        <Pressable
          onPress={onToggleToughLove}
          className={`h-11 px-4 rounded-full items-center justify-center shadow-lg shadow-black/50 flex-row space-x-2 ${
            motivationStyle === "tough-love"
              ? "bg-red-900/50"
              : "bg-zinc-800"
          }`}
          style={{
            borderWidth: 1,
            borderColor:
              motivationStyle === "tough-love"
                ? Colors.status.error
                : "rgba(255,255,255,0.03)",
          }}
        >
          <Text className="text-lg">
            {motivationStyle === "tough-love" ? "🔥" : "💪"}
          </Text>
          <Text
            style={{
              color:
                motivationStyle === "tough-love"
                  ? Colors.status.error
                  : Colors.text.secondary,
            }}
            className="font-medium pr-1"
          >
            {motivationStyle === "tough-love" ? "TOUGH" : " Enable Tough Love"}
          </Text>
        </Pressable>
        <Pressable
          onPress={onStyleSelectorPress}
          className="w-11 h-11 rounded-full items-center justify-center shadow-lg shadow-black/50"
          style={{
            backgroundColor: Colors.background.elevated,
            borderWidth: 1,
            borderColor: "rgba(255,255,255,0.03)",
          }}
        >
          <Text className="text-lg">⚙️</Text>
        </Pressable>
      </View>
    </View>
  );
});

export default HomeHeader;